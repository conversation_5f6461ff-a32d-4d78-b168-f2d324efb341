from lib.bot.open_api import client
import time
import openai
import json
from flask import jsonify
from lib.quoteai_logger.custom_logger import logger as logger
from lib.email.gmail_service import send_email


def chat_with_bot_with_streaming(thread_id, user_input, assistant_id):
    # TODO
    pass


def chat_with_bot_with_polling(thread_id, user_input, assistant_id, tradie_email):
    start = time.time()
    # Adding the message to the new or existing thread.
    client.beta.threads.messages.create(
        thread_id=thread_id, role="user", content=user_input
    )
    try:
        # Run the thread with the new input from the user.
        run = client.beta.threads.runs.create(
            thread_id=thread_id, assistant_id=assistant_id
        )

        while True:
            run = client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run.id)
            logger.info(f"Run Status: {run.status}")
            if run.status == "completed":
                # We break when run complete.
                break
            elif run.status == "requires_action":
                function_name = run.required_action.submit_tool_outputs.tool_calls[
                    0
                ].function.name
                arguments = json.loads(
                    run.required_action.submit_tool_outputs.tool_calls[
                        0
                    ].function.arguments
                )
                full_name = arguments.get("full_name")
                suburb = arguments.get("suburb")
                contact_number = arguments.get("contact_number")
                user_email = arguments.get("email")
                details_of_issue = arguments.get("details_of_issue")
                preferred_time = arguments.get("preferred_time")
                tool_id = run.required_action.submit_tool_outputs.tool_calls[0].id

                if function_name == "post_process":
                    result = post_process(
                        full_name=full_name,
                        suburb=suburb,
                        summary_of_issue=details_of_issue,
                        contact_number=contact_number,
                        tradie_email=tradie_email,
                        preferred_time=preferred_time,
                        user_email=user_email,
                        thread_id=thread_id,
                    )
                    payload_to_gpt = {
                        "tool_call_id": tool_id,
                        "output": json.dumps(result),
                    }
                    logger.info(f"Post_Process Payload {payload_to_gpt}")
                    client.beta.threads.runs.submit_tool_outputs_and_poll(
                        thread_id=thread_id,
                        run_id=run.id,
                        tool_outputs=[payload_to_gpt],
                    )
                    run = client.beta.threads.runs.retrieve(
                        thread_id=thread_id, run_id=run.id
                    )
            elif run.status in ["failed", "cancelled", "expired"]:
                return jsonify({"error": f"Run failed with status: {run.status}"}), 500
            time.sleep(0.5)
        messages = client.beta.threads.messages.list(thread_id=thread_id)
        response = messages.data[0].content[0].text.value
        logger.info(f"Response: {response[:50]}...")
        end = time.time()
        logger.info(f"Time required: {end-start:.2f} seconds")
        return jsonify({"response": response})
    except openai.error.RateLimitError:
        logger.info("Rate limit exceeded.")
    except openai.error.APIConnectionError:
        logger.info("Network connection error.")
    except openai.error.InvalidRequestError as e:
        logger.info(f"Invalid request: {e}")
    except openai.error.AuthenticationError:
        logger.info("Authentication failed.")
    except openai.error.APIError as e:
        logger.info(f"OpenAI server error: {e}")
    except Exception as e:
        logger.info(f"Unexpected error: {e}")

def post_process(full_name, suburb, contact_number, tradie_email, summary_of_issue,preferred_time, user_email, thread_id):
   email_content = {
      "full_name": full_name,
      "phone_number": contact_number,
      "user_email": user_email,
      "suburb": suburb,
      "summary_of_issue": summary_of_issue,
      "preferred_time": preferred_time
   }

   return send_email(
      tradie_email=tradie_email,
      subject="Quote AI: Job Request.",
      email_content=email_content,
      thread_id=thread_id
   )
