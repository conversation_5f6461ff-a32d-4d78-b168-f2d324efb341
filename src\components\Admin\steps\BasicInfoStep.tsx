/**
 * Basic Information Step
 * First step of the onboarding wizard - company details
 */

import React from 'react';
import { motion } from 'framer-motion';
import { useFormContext } from 'react-hook-form';
import { getErrorMessage, hasError } from '../utils/formUtils';

interface BasicInfoStepProps {
  loading: boolean;
  nextStep?: () => void;
  previousStep?: () => void;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  loading,
  nextStep
}) => {
  const { register, formState: { errors }, trigger } = useFormContext();

  const stepVariants = {
    initial: {
      opacity: 0,
      x: 100,
      scale: 0.95,
      rotateY: 10
    },
    animate: {
      opacity: 1,
      x: 0,
      scale: 1,
      rotateY: 0,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 30,
        mass: 0.8,
        duration: 0.6
      }
    },
    exit: {
      opacity: 0,
      x: -100,
      scale: 0.95,
      rotateY: -10,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 40,
        duration: 0.4
      }
    }
  };

  const handleNext = async () => {
    // Validate current step fields
    const isValid = await trigger(['basic_info.company_name', 'basic_info.business_abn', 'basic_info.website_url']);
    if (isValid && nextStep) {
      nextStep();
    }
  };

  return (
    <motion.div
      className="step-content"
      variants={stepVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      style={{
        transformStyle: "preserve-3d",
        backfaceVisibility: "hidden",
        willChange: "transform, opacity"
      }}
    >
      <div className="step-header">
        <h2>Basic Information</h2>
        <p>Let's start with the essential company details</p>
      </div>

      <div className="step-form">
        <div className="form-grid">
          <div className="form-group">
            <label htmlFor="company_name">
              Company Name *
            </label>
            <input
              type="text"
              id="company_name"
              {...register('basic_info.company_name', {
                required: 'Company name is required',
                minLength: {
                  value: 2,
                  message: 'Company name must be at least 2 characters'
                }
              })}
              placeholder="Enter company name"
              disabled={loading}
              className={hasError(errors, 'basic_info.company_name') ? 'error' : ''}
            />
            {hasError(errors, 'basic_info.company_name') && (
              <motion.span
                className="field-error"
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
              >
                {getErrorMessage(errors, 'basic_info.company_name')}
              </motion.span>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="business_abn">
              ABN *
            </label>
            <input
              type="text"
              id="business_abn"
              {...register('basic_info.business_abn', {
                required: 'ABN is required',
                pattern: {
                  value: /^\d{2}\s?\d{3}\s?\d{3}\s?\d{3}$/,
                  message: 'Please enter a valid ABN (11 digits)'
                }
              })}
              placeholder="12 ***********"
              disabled={loading}
              className={hasError(errors, 'basic_info.business_abn') ? 'error' : ''}
            />
            {hasError(errors, 'basic_info.business_abn') && (
              <motion.span
                className="field-error"
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
              >
                {getErrorMessage(errors, 'basic_info.business_abn')}
              </motion.span>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="website_url">
              Website URL *
            </label>
            <input
              type="url"
              id="website_url"
              {...register('basic_info.website_url', {
                required: 'Website URL is required',
                pattern: {
                  value: /^https?:\/\/.+\..+/,
                  message: 'Please enter a valid URL'
                }
              })}
              placeholder="https://example.com"
              disabled={loading}
              className={hasError(errors, 'basic_info.website_url') ? 'error' : ''}
            />
            {hasError(errors, 'basic_info.website_url') && (
              <motion.span
                className="field-error"
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
              >
                {getErrorMessage(errors, 'basic_info.website_url')}
              </motion.span>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="user_type">
              User Type
            </label>
            <select
              id="user_type"
              {...register('basic_info.user_type')}
              disabled={loading}
            >
              <option value="tradie">Tradie</option>
              <option value="business">Business</option>
            </select>
          </div>
        </div>
      </div>

      <div className="step-actions">
        <button
          type="button"
          className="btn-secondary"
          disabled={true}
        >
          Previous
        </button>

        <button
          type="button"
          onClick={handleNext}
          className="btn-primary"
          disabled={loading}
        >
          {loading ? (
            <span className="loading-content">
              <span className="loading-spinner"></span>
              Loading...
            </span>
          ) : (
            'Next Step'
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default BasicInfoStep;
