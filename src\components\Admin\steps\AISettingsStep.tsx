/**
 * AI Settings Step
 * Final step of the onboarding wizard - AI configuration and submission
 */

import React from 'react';
import { motion } from 'framer-motion';
import { useFormContext } from 'react-hook-form';
import { getErrorMessage, hasError } from '../utils/formUtils';

interface AISettingsStepProps {
  onSubmit: () => void;
  loading: boolean;
  nextStep?: () => void;
  previousStep?: () => void;
}

const AISettingsStep: React.FC<AISettingsStepProps> = ({
  onSubmit,
  loading,
  previousStep
}) => {
  const { register, formState: { errors }, watch } = useFormContext();

  const stepVariants = {
    initial: {
      opacity: 0,
      x: 100,
      scale: 0.95,
      rotateY: 10
    },
    animate: {
      opacity: 1,
      x: 0,
      scale: 1,
      rotateY: 0,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 30,
        mass: 0.8,
        duration: 0.6
      }
    },
    exit: {
      opacity: 0,
      x: -100,
      scale: 0.95,
      rotateY: -10,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 40,
        duration: 0.4
      }
    }
  };

  const handlePrevious = () => {
    if (previousStep) {
      previousStep();
    }
  };

  const handleSubmit = () => {
    onSubmit();
  };

  const greetingMessage = watch('ai_settings.greeting_message');
  const customInstructions = watch('ai_settings.custom_instructions');

  return (
    <motion.div
      className="step-content"
      variants={stepVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      style={{
        transformStyle: "preserve-3d",
        backfaceVisibility: "hidden",
        willChange: "transform, opacity"
      }}
    >
      <div className="step-header">
        <h2>AI Settings</h2>
        <p>Customize how the AI assistant interacts with customers</p>
      </div>

      <div className="step-form">
        <div className="form-section">
          <h3>AI Personality</h3>
          <div className="form-grid">
            <div className="form-group form-group-full">
              <label htmlFor="greeting_message">
                Greeting Message
              </label>
              <textarea
                id="greeting_message"
                {...register('ai_settings.greeting_message', {
                  maxLength: {
                    value: 200,
                    message: 'Greeting message must be less than 200 characters'
                  }
                })}
                placeholder="Hello! How can I help you with your project today?"
                disabled={loading}
                rows={3}
                className={hasError(errors, 'ai_settings.greeting_message') ? 'error' : ''}
              />
              <div className="character-count">
                {greetingMessage?.length || 0}/200 characters
              </div>
              {hasError(errors, 'ai_settings.greeting_message') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {getErrorMessage(errors, 'ai_settings.greeting_message')}
                </motion.span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="response_tone">
                Response Tone
              </label>
              <select
                id="response_tone"
                {...register('ai_settings.response_tone')}
                disabled={loading}
              >
                <option value="professional">Professional</option>
                <option value="friendly">Friendly</option>
                <option value="casual">Casual</option>
                <option value="formal">Formal</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="max_quote_amount">
                Max Quote Amount ($)
              </label>
              <input
                type="number"
                id="max_quote_amount"
                {...register('ai_settings.max_quote_amount', {
                  min: { value: 100, message: 'Minimum quote amount is $100' },
                  max: { value: 100000, message: 'Maximum quote amount is $100,000' }
                })}
                min="100"
                max="100000"
                step="100"
                disabled={loading}
                className={hasError(errors, 'ai_settings.max_quote_amount') ? 'error' : ''}
              />
              {hasError(errors, 'ai_settings.max_quote_amount') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {getErrorMessage(errors, 'ai_settings.max_quote_amount')}
                </motion.span>
              )}
            </div>
          </div>
        </div>

        <div className="form-section">
          <h3>Custom Instructions</h3>
          <div className="form-group">
            <label htmlFor="custom_instructions">
              Additional Instructions for AI
            </label>
            <textarea
              id="custom_instructions"
              {...register('ai_settings.custom_instructions', {
                maxLength: {
                  value: 1000,
                  message: 'Custom instructions must be less than 1000 characters'
                }
              })}
              placeholder="Provide any specific instructions for how the AI should handle customer inquiries, pricing guidelines, or special considerations..."
              disabled={loading}
              rows={6}
              className={hasError(errors, 'ai_settings.custom_instructions') ? 'error' : ''}
            />
            <div className="character-count">
              {customInstructions?.length || 0}/1000 characters
            </div>
            {hasError(errors, 'ai_settings.custom_instructions') && (
              <motion.span
                className="field-error"
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
              >
                {getErrorMessage(errors, 'ai_settings.custom_instructions')}
              </motion.span>
            )}
            <small className="field-help">
              These instructions will help the AI provide more accurate and personalized responses to your customers.
            </small>
          </div>
        </div>

        <div className="form-section">
          <div className="completion-summary">
            <h3>Ready to Complete Onboarding</h3>
            <p>
              You've provided all the necessary information. Click "Complete Onboarding" 
              to create the customer profile and activate the AI assistant.
            </p>
            <div className="summary-checklist">
              <div className="checklist-item">
                <span className="check-icon">✓</span>
                Basic company information
              </div>
              <div className="checklist-item">
                <span className="check-icon">✓</span>
                Contact details and address
              </div>
              <div className="checklist-item">
                <span className="check-icon">✓</span>
                Business details and service areas
              </div>
              <div className="checklist-item">
                <span className="check-icon">✓</span>
                AI settings and customization
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="step-actions">
        <button
          type="button"
          onClick={handlePrevious}
          className="btn-secondary"
          disabled={loading}
        >
          Previous
        </button>

        <button
          type="button"
          onClick={handleSubmit}
          className="btn-primary btn-complete"
          disabled={loading}
        >
          {loading ? (
            <span className="loading-content">
              <span className="loading-spinner"></span>
              Creating Customer...
            </span>
          ) : (
            'Complete Onboarding'
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default AISettingsStep;
