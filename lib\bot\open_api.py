# Thread Creation Here. Offcial Doc: https://platform.openai.com/docs/api-reference/assistants/createAssistant

import openai
import lib.bot.jargon as jargon
from openai import OpenAI
from openai.types.beta import assistant
from conf.config import config

openai.api_key = config.OPENAI.ASSISTANT_API_KEY
client = OpenAI(api_key=openai.api_key)


class AssistantThreadCreation:
    def generate_thread_id(self):
        thread = client.beta.threads.create()
        return thread

    def create_thread_for_user(self, user_tradie_type, custom_instructions):
        # Create the assistant
        instruction_for_bot = jargon.jargon_for_plumber(
            tradie_type=user_tradie_type, custom_instructions=None
        )
        description_for_bot = jargon.get_bot_description()
        model_for_bot = jargon.gpt_model_for_free_user()
        functions = [
            {
                "name": "post_process",
                "description": "This function is used to process the all the information we have received from the user.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "full_name": {
                            "type": "string",
                            "description": "The fullname of the user.",
                        },
                        "preferred_time": {
                            "type": "string",
                            "description": "The preferred time the customer wants the job to be done.",
                        },
                        "suburb": {
                            "type": "string",
                            "description": "The suburb the user is located in.",
                        },
                        "contact_number": {
                            "type": "string",
                            "decription": "Mobile Number of the user.",
                        },
                        "email": {
                            "type": "string",
                            "description": "The email address of the user.",
                        },
                        "details_of_issue": {
                            "type": "string",
                            "description": "The description of the issue in detail.",
                        },
                    },
                    "required": [
                        "full_name",
                        "suburb",
                        "contact_number",
                        "email",
                        "details_of_issue",
                        "preferred_time",
                    ],
                },
            }
        ]

        assistant = client.beta.assistants.create(
            description=description_for_bot,
            instructions=instruction_for_bot,
            model=model_for_bot,
            tools=[{"type": "function", "function": functions[0]}],
        )
        thread_id = client.beta.threads.create().id
        assistant_id = assistant.id
        return thread_id, assistant_id
