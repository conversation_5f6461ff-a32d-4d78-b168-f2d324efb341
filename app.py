import os
import uuid
from time import sleep
import openai
import threading
import datetime
import time
import smtplib
import json
import controller
from lib.utils import helper
from db import connection
from openai import OpenAI
from flask import Flask, request, jsonify, send_from_directory, redirect
from openai.types.beta import assistant
from flask_cors import CORS
from lib.bot.open_api import AssistantThreadCreation
from lib.bot.open_api import client
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from lib.email.gmail_service import send_email
from lib.api_auth import authenticate
from werkzeug.utils import secure_filename
from lib.quoteai_logger.custom_logger import logger as logger

# ;C\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL
# TODO: REMOVE: Hardcoded API key
# ADD: Environment variables for sensitive data
# from dotenv import load_dotenv
# load_dotenv()

# API_KEY = os.getenv('API_KEY')
# FIREBASE_CREDENTIALS = os.getenv('FIREBASE_CREDENTIALS')
# OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

# TODO: REPLACE: Dev console prints with proper logging
# import logging
# from logging.handlers import RotatingFileHandler

# # Setup proper logging
# logging.basicConfig(
#     handlers=[RotatingFileHandler('app.log', maxBytes=100000, backupCount=3)],
#     level=logging.INFO,
#     format='%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
# )

# logger = logging.getLogger('quoteai')

# TODO: REPLACE: CORS handling with proper webpage URL origins
# Replace current CORS setup with more restrictive one
# CORS(app, resources={
#     r"/*": {
#         "origins": os.getenv('ALLOWED_ORIGINS', '').split(','),
#         "allow_headers": ["Content-Type", "x-api-key"],
#         "methods": ["GET", "POST", "OPTIONS"]
#     }
# })

# @app.after_request
# def add_security_headers(response):
#     response.headers['X-Content-Type-Options'] = 'nosniff'
#     response.headers['X-Frame-Options'] = 'DENY'
#     response.headers['X-XSS-Protection'] = '1; mode=block'
#     response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
#     return response


# Start the Flask App
logger.info("STARTING QUOTE AI SERVER")
app = Flask(__name__)

# Setup
oat = AssistantThreadCreation()
fb_client = connection.get_db_client_prod()
storage_bucket = connection.get_storage_bucket()

# Thread locks for managing concurrent requests
# thread_locks = {}
# thread_lock_mutex = threading.Lock()


# Configure CORS to allow all necessary endpoints
CORS(
    app,
    resources={r"/*": {"origins": ["https://getquoteai.com"]}},
    allow_headers=["Content-Type", "x-api-key"],
    methods=["GET", "POST", "OPTIONS"]
)

# Configure upload settings
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), "uploads")
ALLOWED_EXTENSIONS = {"png", "jpg", "jpeg", "gif"}
MAX_CONTENT_LENGTH = 8 * 1024 * 1024  # 8MB

# Create uploads directory if it doesn't exist
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config["UPLOAD_FOLDER"] = UPLOAD_FOLDER
app.config["MAX_CONTENT_LENGTH"] = MAX_CONTENT_LENGTH


# API Calls
@app.route("/v1/ping", methods=["GET"])
def is_healthy():
    return jsonify({"status": "ok"})


# Added another health check other than ping for firebase and OpenAI
@app.route("/v1/health")
def health_check():
    try:
        # Check Firebase connection
        fb_client.collection("clients").limit(1).get()
        # Check OpenAI connection
        client.models.list()
        return jsonify({"status": "healthy"}), 200
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500


@app.route("/initiate_conversation", methods=["OPTIONS", "POST"])
def start_converstation():
    """
    This function is used to create the thread_id.
    Store the thread_id to Firestore.
    """
    try:

        # Handle OPTIONS request for CORS preflight by returning a preflight response
        if request.method == "OPTIONS":
            print("Handling OPTIONS request")
            return _build_cors_preflight_response()

        # Handle the data
        logger.info("INITIATE CONVERSATION ENDPOINT CALLED")
        data = request.get_json()
        response = controller.handle_intiate_conversation(data=data)
        return response

    except:
        logger.warning("Initiate Converstaion Failed")
        return jsonify({"message: Chat Endpoint Failed"}), 500


def _build_cors_preflight_response():
    response = app.make_default_options_response()
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, x-api-key"
    return response


# For Starting the conversation
@app.route("/chat", methods=["OPTIONS", "POST"])
def chat():

    # Handle OPTIONS request for CORS preflight by returning a preflight response
    if request.method == "OPTIONS":
        print("Handling OPTIONS request")
        return _build_cors_preflight_response()

    logger.info("\n==== CHAT ENDPOINT CALLED ====\n")
    data = request.get_json()
    print(f"Request data: {data}")
    response = controller.handle_chat(data)
    return response


# Helper function to check if file extension is allowed
def allowed_file(filename):
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


# File upload endpoint
@app.route("/upload", methods=["POST"])
def upload_file():
    # Check if the post request has the file part
    if "file" not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files["file"]
    thread_id = request.form.get("thread_id")

    # If user does not select file, browser also
    # submit an empty part without filename
    if file.filename == "":
        return jsonify({"error": "No selected file"}), 400

    if not thread_id:
        return jsonify({"error": "No thread_id provided"}), 400

    # Get thread details to extract website URL
    thread_doc = fb_client.collection("thread_ids").document(thread_id).get()
    if not thread_doc.exists:
        return jsonify({"error": "Thread not found"}), 404

    thread_details = thread_doc.to_dict()
    website_url = thread_details.get("website_url")

    # Authenticate using API key and domain
    api_key = request.headers.get("X-Api-Key")
    authentication = authenticate(api_key=api_key, domain=website_url)
    if not authentication:
        return (
            jsonify({"Error": "You do not have permission to call this endpoint."}),
            401,
        )

    if file and allowed_file(file.filename):
        try:
            # Generate a unique filename to prevent collisions
            original_filename = secure_filename(file.filename)
            file_extension = (
                original_filename.rsplit(".", 1)[1].lower()
                if "." in original_filename
                else ""
            )
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"

            # Define the path in Firebase Storage
            storage_path = f"uploads/{thread_id}/{unique_filename}"

            # Create a blob in Firebase Storage
            blob = storage_bucket.blob(storage_path)

            # Set content type based on file extension
            content_type = None
            if file_extension == "jpg" or file_extension == "jpeg":
                content_type = "image/jpeg"
            elif file_extension == "png":
                content_type = "image/png"
            elif file_extension == "gif":
                content_type = "image/gif"

            if content_type:
                blob.content_type = content_type

            # Upload the file
            blob.upload_from_file(file)

            # # Make the blob publicly accessible
            # blob.make_public()

            # Get the public URL
            file_url = blob.public_url

            # Store file info in Firestore
            file_doc = {
                "original_filename": original_filename,
                "stored_filename": unique_filename,
                "storage_path": storage_path,
                "file_url": file_url,
                "thread_id": thread_id,
                "upload_time": datetime.datetime.now(),
            }

            # Add to Firestore
            fb_client.collection("attachments").add(file_doc)

            return jsonify(
                {"success": True, "file_url": file_url, "filename": original_filename}
            )
        except Exception as e:
            print(f"Error uploading file: {str(e)}")
            return jsonify({"error": f"Error uploading file: {str(e)}"}), 500

    return jsonify({"error": "File type not allowed"}), 400


# TODO: test if this works as it is copied from the google workspace and remove local file handling
# Redirect to Firebase Storage URL, added from firestore recommendation
@app.route("/files/<thread_id>/<filename>")
def serve_file(thread_id, filename):
    # For backward compatibility, redirect to Firebase Storage
    try:
        # Look up the file in Firestore
        attachments = (
            fb_client.collection("attachments")
            .where("thread_id", "==", thread_id)
            .where("stored_filename", "==", filename)
            .get()
        )

        for attachment in attachments:
            file_data = attachment.to_dict()
            if "file_url" in file_data:
                return redirect(file_data["file_url"])

        # If not found, try to construct the URL
        storage_path = f"uploads/{thread_id}/{filename}"
        blob = storage_bucket.blob(storage_path)
        if blob.exists():
            blob.make_public()
            return redirect(blob.public_url)
    except Exception as e:
        print(f"Error serving file: {str(e)}")

    # Fallback to local file if Firebase Storage fails
    return send_from_directory(
        os.path.join(app.config["UPLOAD_FOLDER"], thread_id), filename
    )
