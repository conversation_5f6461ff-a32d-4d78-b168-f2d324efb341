from google.oauth2 import service_account
from googleapiclient.discovery import build
from email.message import EmailMessage
from typing import List, Tuple
import base64
import os
import mimetypes
print(os.getcwd())

import firebase_admin
from firebase_admin import credentials
from firebase_admin import firestore
from firebase_admin import storage
import os

os.environ["GOOGLE_CLOUD_PROJECT"] = "quoteai-firebase"


def get_db_client():
    cred = credentials.ApplicationDefault()
    firebase_admin.initialize_app(cred, {"projectId": "quoteai-firebase"})
    db_client = firestore.client()
    app = firebase_admin.get_app()
    print("Firestore Project ID:", app.project_id)
    return db_client


# gcloud auth application-default login


def get_db_client_prod():
    try:
        # Try to get the default app if it exists
        app = firebase_admin.get_app()
    except ValueError:
        # Initialize the app if it doesn't exist
        # Use absolute path to the credentials file
        cred_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "quoteai-firebase-firebase-adminsdk-5sbso-6ff415e53e.json",
        )
        cred = credentials.Certificate(cred_path)
        firebase_admin.initialize_app(
            cred, {"storageBucket": "quoteai-firebase.firebasestorage.app"}
        )

    db_client = firestore.client()
    return db_client


def get_storage_bucket():
    try:
        # Try to get the default app if it exists
        app = firebase_admin.get_app()
    except ValueError:
        # Initialize the app if it doesn't exist
        # Use absolute path to the credentials file
        cred_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "quoteai-firebase-firebase-adminsdk-5sbso-6ff415e53e.json",
        )
        cred = credentials.Certificate(cred_path)
        firebase_admin.initialize_app(
            cred, {"storageBucket": "quoteai-firebase.firebasestorage.app"}
        )

    bucket = storage.bucket()
    return bucket



SERVICE_ACCOUNT_FILE = 'quoteai-455304-b07869f30cf6.json'
USER_EMAIL = '<EMAIL>'

SCOPES = ['https://www.googleapis.com/auth/gmail.send']


def get_firebase_images(thread_id: str) -> List[Tuple[str, bytes]]:
    """
    Downloads all images from Firebase Storage under /uploads/<thread_id>/.
    Returns a list of (filename, image_bytes).
    """
    bucket = get_storage_bucket()
    prefix = f"uploads/{thread_id}/"
    blobs = bucket.list_blobs(prefix=prefix)

    images = []
    for blob in blobs:
        if blob.name.endswith('/'):  # Skip folders
            continue
        image_data = blob.download_as_bytes()
        filename = blob.name.split('/')[-1]
        images.append((filename, image_data))
    return images

def attach_images_to_email(message: EmailMessage, images: List[Tuple[str, bytes]]):
    """
    Attaches images to an EmailMessage.
    """
    for filename, image_data in images:
        mime_type, _ = mimetypes.guess_type(filename)
        if not mime_type:
            mime_type = 'application/octet-stream'
        maintype, subtype = mime_type.split('/')
        message.add_attachment(image_data, maintype=maintype, subtype=subtype, filename=filename)


def generate_email_body(full_name, phone_number, email, suburb, preferred_time, issue_summary):
    issue_html = issue_summary.replace('\n', '<br>')
    return f"""
    <html>
    <body>
        <p><a href="https://getquoteai.com">Quote AI</a></p>

        <p><strong>Name:</strong> {full_name}</p>
        <p><strong>Phone Number:</strong> {phone_number}</p>
        <p><strong>Email:</strong> <a href="mailto:{email}">{email}</a></p>
        <p><strong>Suburb:</strong> {suburb}</p>
        <p><strong>Preferred Time:</strong> {preferred_time}</p>

        <p><strong>Outline of Job:</strong><br>
        {issue_html}
        </p>
    </body>
    </html>
    """
def gmail_service():
    credentials = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_FILE, scopes=SCOPES
    )
    delegated_credentials = credentials.with_subject(USER_EMAIL)
    service = build('gmail', 'v1', credentials=delegated_credentials)
    return service

def send_email(tradie_email, subject, email_content, thread_id=None):
    html_body = generate_email_body(
        full_name=email_content.get('full_name',''),
        phone_number=email_content.get("phone_number", ''),
        email=email_content.get('user_email',''),
        suburb=email_content.get('suburb',''),
        preferred_time=email_content.get('preferred_time',''),
        issue_summary=email_content.get('summary_of_issue','')
    )

    message = EmailMessage()
    message.set_content("This is the plain text fallback for clients that don't support HTML.")
    message.add_alternative(html_body, subtype='html')
    message['To'] = tradie_email
    message['From'] = USER_EMAIL
    message['Subject'] = f"{subject} for {email_content.get('full_name', '')}"

    # 🔗 Attach Firebase images if thread_id is provided
    if thread_id:
        images = get_firebase_images(thread_id)
        attach_images_to_email(message, images)

    encoded_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
    send_body = {'raw': encoded_message}

    service = gmail_service()
    result = service.users().messages().send(userId="me", body=send_body).execute()
    return {
        "status" : f"Email sent! Message ID: {result['id']}"
    }


# print(get_firebase_images(thread_id='thread_g04Y7RdZffNjupt7RH26mDwr'))
