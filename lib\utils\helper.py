# Helper Functions here.
import json
from datetime import datetime
import pytz
from lib.quoteai_logger.custom_logger import logger as logger
from db import connection
from lib.bot.open_api import client
from google.cloud import firestore
from google.api_core.exceptions import GoogleAPIError

# Initate Conversation Endpoint Helpers
def check_customer_meta(customer_name):
    fb_client = connection.get_db_client_prod()
    doc_ref = fb_client.collection('customers').document(customer_name)
    try:
        doc= doc_ref.get()
        print(doc.to_dict())
        if doc.exists:
            logger.info("Customer esists.")
            req_data = process_customer_meta_data(doc.to_dict())
            return True, req_data
        else:
            logger.info("Customer does not exists.")
            return False, None
    except Exception as e:
        logger.info(f"Error checking documet as {e}")
        return False, None

def process_customer_meta_data(data):
    '''
    Cherry pick the information from meta data.
    '''
    if data:
        instructions = data.get("ai_settings", {}).get("custom_instructions")
        email = data.get("contact_info", {}).get("primary_email")
        tradie_type = data.get("tradie_details").get("tradie_type")
        required_data = {
            "tradie_email" : email,
            "custom_instructions" : instructions,
            "tradie_type": tradie_type
        }
        return required_data
    
def store_thread_details(customer_name,thread_id, assistant_id, tradie_email):
    fb_client = connection.get_db_client_prod()
    doc_ref = fb_client.collection("thread_ids").document(thread_id)
    au_tz = pytz.timezone("Australia/Sydney")
    now_au = datetime.now(au_tz)
    data = {
        "customer_name": customer_name,
        "thread_id": thread_id,
        "assistant_id": assistant_id,
        "tradie_email": tradie_email,
        "created_at": now_au.isoformat(),         # ISO format with timezone
        "created_date": now_au.strftime("%Y-%m-%d")  # Just the date
    }
    try:
        doc_ref.set(data)
        logger.info(f"Document for '{customer_name}' written successfully.")
    except GoogleAPIError as e:
        logger.info(f"Error writing document: {e}")


# Chat
def fetch_thread_details(thread_id):
    try:
        fb_client = connection.get_db_client_prod()
        doc_ref = fb_client.collection('thread_ids').document(thread_id)
        data = doc_ref.get().to_dict()
        return data
    except GoogleAPIError as e:
        logger.warning(f"Failed to fetch details thread details for chat. {e}")
    return None

def check_for_active_runs(thread_id):
    try:
        # List all runs for the thread
        runs = client.beta.threads.runs.list(thread_id=thread_id)

        # Check if any run is in progress
        for run in runs.data:
            # Access status as a property or dictionary key, depending on the OpenAI version
            run_status = getattr(run, 'status', None)
            if run_status is None and hasattr(run, 'get'):
                # Handle dictionary-like objects
                run_status = run.get('status')

            if run_status in ['queued', 'in_progress', 'requires_action']:
                # Access id as a property or dictionary key
                run_id = getattr(run, 'id', None)
                if run_id is None and hasattr(run, 'get'):
                    run_id = run.get('id')
                return True, run_id or ''

        return False, ""
    except Exception as e:
        print(f"Error checking active runs: {e}")
        return False, ""



