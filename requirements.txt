annotated-types==0.7.0
anyio==4.9.0
black==25.1.0
blinker==1.9.0
CacheControl==0.14.2
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cryptography==44.0.2
distro==1.9.0
firebase-admin==6.7.0
Flask==3.1.0
flask-cors==5.0.1
google-api-core==2.24.2
google-api-python-client==2.166.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.1
google-cloud-core==2.4.3
google-cloud-firestore==2.20.1
google-cloud-storage==3.1.0
google-crc32c==1.7.1
google-resumable-media==2.7.2
googleapis-common-protos==1.69.2
grpcio==1.71.0
grpcio-status==1.71.0
gunicorn==23.0.0
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.1
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.6
jiter==0.9.0
lxml==5.3.1
MarkupSafe==3.0.2
msgpack==1.1.0
munch==4.0.0
mypy_extensions==1.1.0
oauthlib==3.2.2
openai==1.70.0
packaging==25.0
pathspec==0.12.1
platformdirs==4.3.8
proto-plus==1.26.1
protobuf==5.29.4
pyaml==25.1.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.2
pydantic_core==2.33.1
PyJWT==2.10.1
pyparsing==3.2.3
python-docx==1.1.2
pytz==2025.2
PyYAML==6.0.2
requests==2.32.3
requests-oauthlib==2.0.0
rsa==4.9
sniffio==1.3.1
tqdm==4.67.1
typing-inspection==0.4.0
typing_extensions==4.13.1
uritemplate==4.1.1
urllib3==2.3.0
Werkzeug==3.1.3