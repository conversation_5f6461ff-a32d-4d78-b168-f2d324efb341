import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart


def post_process(
    full_name, suburb, contact_number, email, summary_of_issue, preferred_time
):

    # email code
    # Set up your email credentials and SMTP server details
    sender_email = "<EMAIL>"
    recipient_email = email
    password = (
        "Quoteme247!$"  # If you're using Gmail, you might need an app-specific password
    )

    # Create the email content
    subject = "Quote AI - New Quote Request"
    body = f"""The job is for:\n\n
Full Name: {full_name}\n
Summary: {summary_of_issue}\n
Contact Number: {contact_number}\n
Suburb:{suburb}\n\n
Preferred Time: {preferred_time}\n\n
"""

    # Set up the MIME
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = recipient_email
    message["Subject"] = subject

    # Attach the body with the email
    message.attach(MIMEText(body, "plain"))

    # Connect to Gmail's SMTP server and send the email
    try:
        # Establish a secure session with G<PERSON>'s SMTP server
        server = smtplib.SMTP("smtp.gmail.com", 587)
        server.starttls()  # Secure the connection
        server.login(sender_email, password)
        text = message.as_string()

        # Send the email
        server.sendmail(sender_email, recipient_email, text)
        print("Email sent successfully!")

    except Exception as e:
        print(f"Failed to send email: {str(e)}")

    finally:
        server.quit()
    return {"message": "Your inquiry has been sent to the plumber"}


print(
    post_process(
        full_name="Syed",
        suburb="Punchbowl",
        contact_number="123123",
        email="<EMAIL>",
        summary_of_issue="TESTIN TESTING",
        preferred_time="Mornings",
    )
)
