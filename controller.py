from db import connection
from flask import session, jsonify
from lib.bot.open_api import AssistantThreadCreation
from lib.utils import helper
from lib.bot.chat import chat_with_bot_with_polling


# Setup
assistant_ai = AssistantThreadCreation()


# API Handling

# Intiate Conversation
def handle_intiate_conversation(data):
    '''
    Used to generate a thread_id for conversation with the end user.
    '''
    if not data: 
        return jsonify({"message": "Request body has no fields."}),400
    customer_name = data.get("customer_name")
    if not customer_name:
        return jsonify({"message": "Request body missing required fields."}), 400
    exists, req_data = helper.check_customer_meta(customer_name=customer_name)
    if not exists: 
        return jsonify({"message": "Customer does not exist"}), 404
    
    # Create thread.
    thread_id, assistant_id = assistant_ai.create_thread_for_user(
        user_tradie_type=req_data.get('tradie_type'),
        custom_instructions=req_data.get('custom_instructions')
    )
    # Store details in firebase.
    helper.store_thread_details(customer_name=customer_name,thread_id=thread_id, assistant_id=assistant_id, tradie_email=req_data.get("tradie_email"))
    return jsonify({
        "success": "true",
        "thread_id": thread_id
    }), 200
    
# Chat API
def handle_chat(data):
    if not data:
        return jsonify({"message": "Request body has no fields"}), 400
    
    # Extract from request body
    thread_id = data.get("thread_id")
    user_input = data.get("message")
    attachments = data.get('attachments', [])
    if not thread_id and not user_input:
        # None check
        return jsonify({"message:": "Request body has missing required fields"}), 400
    
    # Fetch thread details
    
    thread_details = helper.fetch_thread_details(thread_id=thread_id)
    print(thread_details)
    if not thread_details:
        return jsonify({"message": "Failed to fetch thread details for chat."}), 500
    
    assistant_id = thread_details.get("assistant_id")
    tradie_email = thread_details.get("tradie_email")

    if not assistant_id or not tradie_email:
        return jsonify({"message": "Failed to fetch assistant or tradie_email for chat."}), 500

    # Chat

    has_run, run_id = helper.check_for_active_runs(thread_id)
    if has_run:
        return jsonify({"error": f"Thread already has an active run (ID: {run_id}). Please wait for it to complete."}), 409
    
    message_content = user_input

    # If there are attachments, add them to the message
    if attachments and len(attachments) > 0:
        # Format message with image links
        attachment_text = "\n\nAttached images:\n"
        for i, attachment_url in enumerate(attachments):
            attachment_text += f"[Image {i+1}]({attachment_url})\n"

        message_content += attachment_text
    
    assistant_response = chat_with_bot_with_polling(
        thread_id=thread_id,
        user_input=message_content,
        assistant_id=assistant_id,
        tradie_email=tradie_email
    )
    return assistant_response




    
    


    
    # if not data:
    #     return jsonify({"error": "No data provided"}), 400

    # # Get thread ID and extract website URL from thread details
    # thread_id = data.get("thread_id")
    # if not thread_id:
    #     return jsonify({"error": "No thread_id provided"}), 400

    # # Get thread details to extract website URL
    # thread_doc = fb_client.collection("thread_ids").document(thread_id).get()
    # if not thread_doc.exists:
    #     return jsonify({"error": "Thread not found"}), 404

    # thread_details = thread_doc.to_dict()
    # website_url = thread_details.get("website_url")

    # # Authenticate using API key and domain
    # api_key = request.headers.get("X-Api-Key")
    # authentication = authenticate(api_key=api_key, domain=website_url)
    # if not authentication:
    #     return (
    #         jsonify({"Error": "You do not have permission to call this endpoint."}),
    #         401,
    #     )

    # # We already validated data and thread_id above

    # user_input = data.get("message", "")
    # if not user_input:
    #     return jsonify({"error": "No message provided"}), 400

    # # Get attachments if any
    # attachments = data.get("attachments", [])
    # print(f"thread_id: {thread_id}")
    # print(f"user_input: {user_input}")
    # print(f"attachments: {attachments}")

    # # Get or create a lock for this thread
    # with thread_lock_mutex:
    #     if thread_id not in thread_locks:
    #         thread_locks[thread_id] = threading.Lock()

    # # Try to acquire the lock with a timeout
    # lock_acquired = thread_locks[thread_id].acquire(timeout=1)  # 1 second timeout

    # if not lock_acquired:
    #     return (
    #         jsonify(
    #             {"error": "Server is busy processing another request for this thread"}
    #         ),
    #         429,
    #     )

    # try:
    #     # Check if thread has an active run
    #     has_run, run_id = has_active_run(thread_id)
    #     if has_run:
    #         return (
    #             jsonify(
    #                 {
    #                     "error": f"Thread already has an active run (ID: {run_id}). Please wait for it to complete."
    #                 }
    #             ),
    #             409,
    #         )

    #     # Get thread details
    #     thread_doc = fb_client.collection("thread_ids").document(thread_id).get()
    #     if not thread_doc.exists:
    #         return jsonify({"error": "Thread not found"}), 404

    #     thread_details = thread_doc.to_dict()

    #     tradie_email = thread_details.get("tradie_email")
    #     if not tradie_email:
    #         return jsonify({"error": "Tradie email not found in thread details"}), 400

    #     assisstant_id = thread_details.get("assisstant_id")
    #     if not assisstant_id:
    #         return jsonify({"error": "Assistant ID not found in thread details"}), 400

    #     # Prepare message content
    #     message_content = user_input

    #     # If there are attachments, add them to the message
    #     if attachments and len(attachments) > 0:
    #         # Format message with image links
    #         attachment_text = "\n\nAttached images:\n"
    #         for i, attachment_url in enumerate(attachments):
    #             attachment_text += f"[Image {i+1}]({attachment_url})\n"

    #         message_content += attachment_text

    #     # Step 2: Add a message to the thread
    #     client.beta.threads.messages.create(
    #         thread_id=thread_id, role="user", content=message_content
    #     )

    #     # Step 3: Run the Assistant (updated with wait before response)
    #     try:
    #         run = client.beta.threads.runs.create(
    #             thread_id=thread_id, assistant_id=assisstant_id
    #         )

    #         # Step 4: Check the Run Status
    #         # The code continuously checks the status of the assistant run.
    #         # It waits until the run is completed before proceeding.
    #         while True:
    #             run = client.beta.threads.runs.retrieve(
    #                 thread_id=thread_id, run_id=run.id
    #             )
    #             print(f"Run status: {run.status}")

    #             if run.status == "completed":
    #                 break
    #             elif run.status == "requires_action":
    #                 function_name = run.required_action.submit_tool_outputs.tool_calls[
    #                     0
    #                 ].function.name
    #                 arguments = json.loads(
    #                     run.required_action.submit_tool_outputs.tool_calls[
    #                         0
    #                     ].function.arguments
    #                 )
    #                 full_name = arguments.get("full_name")
    #                 suburb = arguments.get("suburb")
    #                 contact_number = arguments.get("contact_number")
    #                 user_email = arguments.get("email")
    #                 summary_of_issue = arguments.get("summary_of_issue")
    #                 preferred_time = arguments.get("preferred_time")
    #                 tool_id = run.required_action.submit_tool_outputs.tool_calls[0].id

    #                 if function_name == "post_process":
    #                     result = post_process(
    #                         full_name=full_name,
    #                         suburb=suburb,
    #                         summary_of_issue=summary_of_issue,
    #                         contact_number=contact_number,
    #                         tradie_email="<EMAIL>",
    #                         preferred_time=preferred_time,
    #                         user_email=user_email,
    #                     )
    #                     payload_to_gpt = {
    #                         "tool_call_id": tool_id,
    #                         "output": json.dumps(result),
    #                     }
    #                     print(payload_to_gpt)
    #                     client.beta.threads.runs.submit_tool_outputs_and_poll(
    #                         thread_id=thread_id,
    #                         run_id=run.id,
    #                         tool_outputs=[payload_to_gpt],
    #                     )
    #                     run = client.beta.threads.runs.retrieve(
    #                         thread_id=thread_id, run_id=run.id
    #                     )
    #             elif run.status in ["failed", "cancelled", "expired"]:
    #                 return (
    #                     jsonify({"error": f"Run failed with status: {run.status}"}),
    #                     500,
    #                 )

    #             # Wait a bit before checking again
    #             time.sleep(0.5)

    #         # Retrieve and return the latest message from the assistant
    #         messages = client.beta.threads.messages.list(thread_id=thread_id)
    #         response = messages.data[0].content[0].text.value
    #         print(f"Response: {response[:50]}...")

    #         end = time.time()
    #         print(f"Time required: {end-start:.2f} seconds")
    #         return jsonify({"response": response})

    #     except Exception as e:
    #         print(f"Error during OpenAI API call: {e}")
    #         return jsonify({"error": str(e)}), 500

    # finally:
    #     # Always release the lock
    #     thread_locks[thread_id].release()




# def chat():
   
#   # Step 1: Fetch the details.
#    start = time.time()
#      # Authenticate. 
#    api_key = request.headers.get("X-Api-Key")
#    authentication = authenticate(api_key=api_key)
#    if not authentication:
#       return jsonify({"Error": "You do not have permission to call this endpoint."})
#    data = request.get_json()
#    thread_id = data.get('thread_id')
#    user_input = data.get('message', '')
  
#   # TODO: DB Operation.
#    thread_details = fb_client.collection("thread_ids").document(thread_id).get().to_dict()
#    tradie_email = thread_details.get('tradie_email')
 
#    assisstant_id = thread_details.get("assisstant_id")

#   # Step 2: Add a message to the thread
#    client.beta.threads.messages.create(thread_id=thread_id,
#                                         role="user",
#                                         content=user_input)
#   # Step 4: Run the Assistant
#    run = client.beta.threads.runs.create_and_poll(thread_id=thread_id,
#                                           assistant_id=assisstant_id)

#   #Step 5: Check the Run Status
#   # The code continuously checks the status of the assistant run.
#   # It waits until the run is completed before proceeding.
#    while True:
#     run = client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run.id)
#     print(run)
#     print(f"Run status: {run.status}")
#     if run.status == 'completed':
#       break
#     elif run.status == "requires_action":
#        function_name = run.required_action.submit_tool_outputs.tool_calls[0].function.name
#        arguments = json.loads(run.required_action.submit_tool_outputs.tool_calls[0].function.arguments)
#        full_name =arguments.get("full_name")
#        suburb = arguments.get("suburb")
#        contact_number = arguments.get("contact_number")
#        user_email = arguments.get("email")
#        summary_of_issue = arguments.get("summary_of_issue")
#        preferred_time = arguments.get("preferred_time")
#        tool_id = run.required_action.submit_tool_outputs.tool_calls[0].id

#        if function_name == 'post_process':
#           result = post_process(
#              full_name=full_name,
#              suburb=suburb,
#              summary_of_issue=summary_of_issue,
#              contact_number=contact_number,
#              tradie_email=tradie_email,
#              preferred_time=preferred_time,
#              user_email=user_email
#           )
#           payload_to_gpt = {
#                 "tool_call_id" : tool_id,
#                 "output": json.dumps(result)
#             }
#           print(payload_to_gpt)
#           client.beta.threads.runs.submit_tool_outputs_and_poll(
#                 thread_id=thread_id,
#                 run_id=run.id,
#                 tool_outputs=[payload_to_gpt]
#             )
#           run = client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run.id)

       
#    #  sleep(10)

#   # Retrieve and return the latest message from the assistant
#    messages = client.beta.threads.messages.list(thread_id=thread_id)
#    print(f"Messages: {messages}")
#    response = messages.data[0].content[0].text.value
#    print(f"The response is {response}")
#    end = time.time()
#    print(f"Time required: {end-start}")
#    return jsonify({"response": response})