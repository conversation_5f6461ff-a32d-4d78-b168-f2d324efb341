<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Name Extraction Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Customer Name Extraction Test</h1>
    <p>This page tests the customer name extraction logic that matches the updated <code>initiateConversationApi</code> function.</p>

    <div class="test-container">
        <h2>Current URL Analysis</h2>
        <div id="current-url-result"></div>
    </div>

    <div class="test-container">
        <h2>Test Different URL Patterns</h2>
        <p>Enter a URL path to test the customer name extraction:</p>
        <input type="text" id="test-url" placeholder="/chat/XY_plumbing" value="/chat/XY_plumbing">
        <button onclick="testUrlExtraction()">Test URL</button>
        <div id="test-result"></div>
    </div>

    <div class="test-container">
        <h2>Predefined Test Cases</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <div id="all-tests-result"></div>
    </div>

    <script>
        // Customer name extraction function (matches the one in apiService.ts)
        function extractCustomerNameFromUrl(testPath = null) {
            const path = testPath || window.location.pathname;
            const pathMatch = path.match(/\/chat\/([^\/]+)/);
            if (pathMatch) {
                return decodeURIComponent(pathMatch[1]);
            }
            return null;
        }

        // Test current URL
        function analyzeCurrentUrl() {
            const currentPath = window.location.pathname;
            const customerName = extractCustomerNameFromUrl();
            
            let resultHtml = `
                <div class="info">
                    <strong>Current URL:</strong> ${window.location.href}<br>
                    <strong>Path:</strong> ${currentPath}<br>
                    <strong>Extracted Customer Name:</strong> ${customerName || 'None'}
                </div>
            `;
            
            if (customerName) {
                resultHtml += `<div class="success">✅ Customer name successfully extracted: "${customerName}"</div>`;
            } else {
                resultHtml += `<div class="error">❌ No customer name found in current URL</div>`;
            }
            
            document.getElementById('current-url-result').innerHTML = resultHtml;
        }

        // Test a specific URL
        function testUrlExtraction() {
            const testUrl = document.getElementById('test-url').value;
            const customerName = extractCustomerNameFromUrl(testUrl);
            
            let resultHtml = `
                <div class="info">
                    <strong>Test Path:</strong> ${testUrl}<br>
                    <strong>Extracted Customer Name:</strong> ${customerName || 'None'}
                </div>
            `;
            
            if (customerName) {
                resultHtml += `<div class="success">✅ Customer name successfully extracted: "${customerName}"</div>`;
            } else {
                resultHtml += `<div class="error">❌ No customer name found in test URL</div>`;
            }
            
            document.getElementById('test-result').innerHTML = resultHtml;
        }

        // Run all predefined tests
        function runAllTests() {
            const testCases = [
                { path: '/chat/XY_plumbing', expected: 'XY_plumbing' },
                { path: '/chat/demo', expected: 'demo' },
                { path: '/chat/ABC-Electrical', expected: 'ABC-Electrical' },
                { path: '/chat/john_smith_plumbing', expected: 'john_smith_plumbing' },
                { path: '/chat/test%20company', expected: 'test company' }, // URL encoded
                { path: '/chat/tradie-name/', expected: 'tradie-name' }, // With trailing slash
                { path: '/chat/', expected: null }, // Empty tradie name
                { path: '/other/page', expected: null }, // Wrong path
                { path: '/chat', expected: null }, // No tradie name
                { path: '/', expected: null }, // Root path
            ];

            let resultsHtml = '<h3>Test Results:</h3>';
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = extractCustomerNameFromUrl(testCase.path);
                const passed = result === testCase.expected;
                
                if (passed) passCount++;
                
                resultsHtml += `
                    <div class="${passed ? 'success' : 'error'}">
                        <strong>Test ${index + 1}:</strong> ${testCase.path}<br>
                        Expected: ${testCase.expected || 'null'}<br>
                        Got: ${result || 'null'}<br>
                        ${passed ? '✅ PASS' : '❌ FAIL'}
                    </div>
                `;
            });

            resultsHtml += `
                <div class="info">
                    <strong>Summary:</strong> ${passCount}/${totalCount} tests passed
                </div>
            `;

            document.getElementById('all-tests-result').innerHTML = resultsHtml;
        }

        // Run current URL analysis on page load
        document.addEventListener('DOMContentLoaded', () => {
            analyzeCurrentUrl();
        });
    </script>
</body>
</html>
