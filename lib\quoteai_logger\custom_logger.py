import logging


def customer_logger():
    # Setting up basic logger that sends logs to console with location info
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(filename)s:%(funcName)s:%(lineno)d - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    return logging.getLogger("quoteai-logger")


# TODO: Setup Handlers. StreamHandlers --> Dev Testing Logs to Console. TimedRotatingFileHandler --> For production logs.
logger = customer_logger()
