import json
import openai
from openai import OpenAI
from openai.types.beta import assistant
import time

# Global
openai.api_key = "********************************************************************************************************************************************************************"
# TODO: Store this key securely.
# TODO: This key was created using <PERSON>'s personal email. Need to create this an official quote ai open ai account and get the key.
client = OpenAI(api_key=openai.api_key)


def get_weather(city):
    return {"city": city, "temperature": "22°C", "condition": "Sunny"}


# Define a function the assistant can call
functions = [
    {
        "name": "get_weather",
        "description": "Get the weather for a given city.",
        "parameters": {
            "type": "object",
            "properties": {
                "city": {"type": "string", "description": "The name of the city."}
            },
            "required": ["city"],
        },
    }
]

# Create an assistant with function calling capabilities
assistant = client.beta.assistants.create(
    name="WeatherBot",
    instructions="You can fetch the weather by calling the get_weather function.",
    model="gpt-4-turbo",
    tools=[{"type": "function", "function": functions[0]}],
)
print(f"The assistant is {assistant}")

print(f"Assistant ID: {assistant.id}")
# Create a new thread
thread = client.beta.threads.create()

# Send a message to the assistant
message = client.beta.threads.messages.create(
    thread_id=thread.id, role="user", content="What is the weather in Sydney?"
)
# Run the assistant in the thread
run = client.beta.threads.runs.create_and_poll(
    thread_id=thread.id, assistant_id=assistant.id
)

print(f"Run Object: {run}")

# Polling for completion
while run.status in ["queued", "in_progress", "completed", "requires_action"]:
    time.sleep(2)
    print(run, end="\n\n")
    if run.status == "completed":
        break
    if run.status == "requires_action":
        # Function calling
        # Find the function being called from the run object.
        function_name = run.required_action.submit_tool_outputs.tool_calls[
            0
        ].function.name
        arguments = json.loads(
            run.required_action.submit_tool_outputs.tool_calls[0].function.arguments
        ).get("city")
        tool_id = run.required_action.submit_tool_outputs.tool_calls[0].id
        if function_name == "get_weather":
            result = get_weather(arguments)  # Return of the function.
            print(result)
            payload_to_gpt = {"tool_call_id": tool_id, "output": json.dumps(result)}
            print(payload_to_gpt)

            # return the result
            client.beta.threads.runs.submit_tool_outputs_and_poll(
                thread_id=thread.id, run_id=run.id, tool_outputs=[payload_to_gpt]
            )
    run = client.beta.threads.runs.retrieve(
        thread_id=thread.id, run_id=run.id
    )  # Run status update

# Fetch the latest messages
messages = client.beta.threads.messages.list(thread_id=thread.id)
for msg in messages.data:
    print(f"{msg.role}: {msg.content}")


# # Check if the assistant called a function
# if run.required_action and "function_call" in run.required_action:
#     function_call = run.required_action["function_call"]
#     function_name = function_call["name"]
#     arguments = json.loads(function_call["arguments"])

#     if function_name == "get_weather":

#         result = get_weather(arguments["city"])

#         # Send the function result back to the assistant
#         client.beta.threads.messages.create(
#             thread_id=thread.id,
#             role="function",
#             name="get_weather",
#             content=json.dumps(result)
#         )

#         # Re-run the assistant with the function result
#         run = client.beta.threads.runs.create(
#             thread_id=thread.id,
#             assistant_id=assistant.id
#         )

#         # Fetch the assistant's final response
#         while run.status in ["queued", "in_progress"]:
#             time.sleep(2)
#             run = client.beta.threads.runs.retrieve(thread_id=thread.id, run_id=run.id)

#         messages = client.beta.threads.messages.list(thread_id=thread.id)
#         for msg in messages.data:
#             print(f"{msg.role}: {msg.content}")
