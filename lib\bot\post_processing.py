# Post Processing is basically getting the user information from the bot.
import re
import time
from lib.bot.open_api import client

"""
Stages of end of converstion. 

Step 0: Trigger for the end of converstaion
- Trigger will be added using a question --> "Is that all you need?"
- Based on the user's response...
- Need to test this using the Assistant Playground. Set this up as an instruction.

Yes -> Assistant API funtion calling. 

Step 1: Ask the bot to do this? 
- List out the user information in details - Name, Full Address, Preferred Time, Email Address, Phone Number
- How will the function calling state know about the thread_id and the assistant id.

Step2: Extract the user information

Step3: Send user to tradie -> email, text, CRM

"""


def chat_with_bot(thread_id, user_input, assistant_id):
    start = time.time()
    # Adding the message to the new or existing thread.
    client.beta.threads.messages.create(
        thread_id=thread_id, role="user", content=user_input
    )  # TODO: Understand what is the role for.

    # Run the thread with the new input from the user.
    run = client.beta.threads.runs.create(
        thread_id=thread_id, assistant_id=assistant_id
    )

    # While the thread runs we need to monitor its status
    while True:
        run = client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run.id)
        print(run)
        print(f"Run status: {run.status}")
        if run.status == "completed":
            break

    messages = client.beta.threads.messages.list(thread_id=thread_id)
    print(f"Messages: {messages}")
    response = messages.data[0].content[0].text.value
    print(f"The response is {response}")
    end = time.time()
    print(f"Time required: {end-start}")

    return response


def extract_details(input_string):
    # Define regex patterns for each field
    patterns = {
        "name": r"(?i)name: (.+)",
        "full_address": r"(?i)full address: (.+)",
        "preferred_time": r"(?i)preferred time: (.+)",
        "email_address": r"(?i)email address: (.+)",
        "phone_number": r"(?i)phone number: (.+)",
    }

    # Extract details using regex
    details = {}
    for key, pattern in patterns.items():
        match = re.search(pattern, input_string)
        details[key] = match.group(1).strip() if match else None

    return details


def send_email():
    pass


def post_process(thread_id):
    pass


print(client)
# # TODO: Check if passing the db connection a good design pattern
# def chat_with_bot():
#     """
#     To chat we need the thread id, user_input, assistant_id
#     """

#     # Step 3: Add a message to the thread
#     client.beta.threads.messages.create(thread_id=thread_id,
#                                             role="user",
#                                             content=user_input)
#     # Step 4: Run the Assistant


#     #Step 5: Check the Run Status
#     # The code continuously checks the status of the assistant run.
#     # It waits until the run is completed before proceeding.


#     # Retrieve and return the latest message from the assistant
#     messages = client.beta.threads.messages.list(thread_id=thread_id)
#     print(f"Messages: {messages}")
#     response = messages.data[0].content[0].text.value
#     print(f"The response is {response}")
#     end = time.time()
#     print(f"Time required: {end-start}")
#     return jsonify({"response": response})


# def chat():
#   start = time.time()
#   data = request.get_json()
#   print(data)
#   thread_id = data.get('thread_id')
#   user_input = data.get('message', '')
#   print(f"thread_id: {thread_id}")
#   print(f"user_input: {user_input}")

#   thread_details = fb_client.collection("thread_ids").document(thread_id).get().to_dict()
#   print(thread_details)
#   assisstant_id = thread_details.get("assisstant_id")
